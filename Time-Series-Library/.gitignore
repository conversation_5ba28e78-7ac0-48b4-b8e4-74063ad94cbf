# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
/scripts/long_term_forecast/Traffic_script/PatchTST1.sh
/backups/
/result.xlsx
/~$result.xlsx
/Time-Series-Library.zip
/temp.sh

.idea
/tv_result.xlsx
/test.py
/m4_results/
/test_results/
/PatchTST_results.xlsx
/seq_len_long_term_forecast/
/progress.xlsx
/scripts/short_term_forecast/PatchTST_M4.sh
/run_tv.py

/scripts/long_term_forecast/ETT_tv_script/
/dataset/
/data/
data_factory_all.py
data_loader_all.py
/scripts/short_term_forecast/tv_script/
/exp/exp_short_term_forecasting_tv.py
/exp/exp_long_term_forecasting_tv.py
/timesnetv2.xlsx
/scripts/anomaly_detection/tmp/
/scripts/imputation/tmp/
/utils/self_tools.py
/scripts/exp_scripts/

checkpoints/
results/
result_long_term_forecast.txt
result_anomaly_detection.txt
scripts/augmentation/
run_anylearn.py
environment.txt
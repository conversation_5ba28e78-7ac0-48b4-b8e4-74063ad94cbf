{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3fdbc8cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple\n", "Requirement already satisfied: neuralforecast in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (3.0.1)\n", "Requirement already satisfied: coreforecast>=0.0.6 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (0.0.16)\n", "Requirement already satisfied: fsspec in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (2025.5.1)\n", "Requirement already satisfied: numpy>=1.21.6 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (1.23.5)\n", "Requirement already satisfied: pandas>=1.3.5 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (1.5.3)\n", "Collecting torch<=2.6.0,>=2.0.0 (from neuralforecast)\n", "  Using cached https://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/40/bb/feb5644baa621fd8e1e88bf51f6fa38ab3f985d472a764144ff4867ac1d6/torch-2.6.0-cp39-cp39-manylinux1_x86_64.whl (766.7 MB)\n", "Requirement already satisfied: pytorch-lightning>=2.0.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (2.5.1.post0)\n", "Requirement already satisfied: ray>=2.2.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray[tune]>=2.2.0->neuralforecast) (2.46.0)\n", "Requirement already satisfied: optuna in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (4.3.0)\n", "Requirement already satisfied: utilsforecast>=0.2.3 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from neuralforecast) (0.2.12)\n", "Requirement already satisfied: filelock in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (4.13.2)\n", "Requirement already satisfied: networkx in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (3.2.1)\n", "Requirement already satisfied: jinja2 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (3.1.6)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from torch<=2.6.0,>=2.0.0->neuralforecast) (12.4.127)\n", "Collecting triton==3.2.0 (from torch<=2.6.0,>=2.0.0->neuralforecast)\n", "  Using cached https://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bc/74/9f12bdedeb110242d8bb1bd621f6605e753ee0cbf73cf7f3a62b8173f190/triton-3.2.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (253.1 MB)\n", "Collecting sympy==1.13.1 (from torch<=2.6.0,>=2.0.0->neuralforecast)\n", "  Using cached https://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/fe/81695a1aa331a842b582453b605175f419fe8540355886031328089d840a/sympy-1.13.1-py3-none-any.whl (6.2 MB)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from sympy==1.13.1->torch<=2.6.0,>=2.0.0->neuralforecast) (1.3.0)\n", "Requirement already satisfied: python-dateutil>=2.8.1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pandas>=1.3.5->neuralforecast) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pandas>=1.3.5->neuralforecast) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from python-dateutil>=2.8.1->pandas>=1.3.5->neuralforecast) (1.17.0)\n", "Requirement already satisfied: tqdm>=4.57.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pytorch-lightning>=2.0.0->neuralforecast) (4.64.1)\n", "Requirement already satisfied: PyYAML>=5.4 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pytorch-lightning>=2.0.0->neuralforecast) (6.0.2)\n", "Requirement already satisfied: torchmetrics>=0.7.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pytorch-lightning>=2.0.0->neuralforecast) (1.7.2)\n", "Requirement already satisfied: packaging>=20.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pytorch-lightning>=2.0.0->neuralforecast) (25.0)\n", "Requirement already satisfied: lightning-utilities>=0.10.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from pytorch-lightning>=2.0.0->neuralforecast) (0.14.3)\n", "Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (3.12.12)\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (1.3.2)\n", "Requirement already satisfied: async-timeout<6.0,>=4.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (5.0.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (1.20.1)\n", "Requirement already satisfied: idna>=2.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from yarl<2.0,>=1.17.0->aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]>=2022.5.0->pytorch-lightning>=2.0.0->neuralforecast) (3.10)\n", "Requirement already satisfied: setuptools in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from lightning-utilities>=0.10.0->pytorch-lightning>=2.0.0->neuralforecast) (78.1.1)\n", "Requirement already satisfied: click>=7.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (8.1.8)\n", "Requirement already satisfied: jsonschema in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (4.24.0)\n", "Requirement already satisfied: msgpack<2.0.0,>=1.0.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (1.1.0)\n", "Requirement already satisfied: protobuf!=3.19.5,>=3.15.3 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (6.31.1)\n", "Requirement already satisfied: requests in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (2.32.3)\n", "Requirement already satisfied: tensorboardX>=1.9 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray[tune]>=2.2.0->neuralforecast) (2.6.2.2)\n", "Requirement already satisfied: pyarrow>=9.0.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from ray[tune]>=2.2.0->neuralforecast) (20.0.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from jinja2->torch<=2.6.0,>=2.0.0->neuralforecast) (3.0.2)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from jsonschema->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from jsonschema->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from jsonschema->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (0.25.1)\n", "Requirement already satisfied: alembic>=1.5.0 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from optuna->neuralforecast) (1.16.1)\n", "Requirement already satisfied: colorlog in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from optuna->neuralforecast) (6.9.0)\n", "Requirement already satisfied: sqlalchemy>=1.4.2 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from optuna->neuralforecast) (2.0.41)\n", "Requirement already satisfied: <PERSON><PERSON> in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from alembic>=1.5.0->optuna->neuralforecast) (1.3.10)\n", "Requirement already satisfied: tomli in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from alembic>=1.5.0->optuna->neuralforecast) (2.2.1)\n", "Requirement already satisfied: greenlet>=1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from sqlalchemy>=1.4.2->optuna->neuralforecast) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from requests->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from requests->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages (from requests->ray>=2.2.0->ray[tune]>=2.2.0->neuralforecast) (2025.4.26)\n", "Installing collected packages: triton, sympy, torch\n", "\u001b[2K  Attempting uninstall: triton\n", "\u001b[2K    Found existing installation: triton 2.0.0\n", "\u001b[2K    Uninstalling triton-2.0.0:\n", "\u001b[2K      Successfully uninstalled triton-2.0.0\n", "\u001b[2K  Attempting uninstall: sympy━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0/3\u001b[0m [triton]\n", "\u001b[2K    Found existing installation: sympy 1.11.1[0m \u001b[32m0/3\u001b[0m [triton]\n", "\u001b[2K    Uninstalling sympy-1.11.1:╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/3\u001b[0m [sympy]\n", "\u001b[2K      Successfully uninstalled sympy-1.11.1━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/3\u001b[0m [sympy]\n", "\u001b[2K  Attempting uninstall: torchm╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/3\u001b[0m [sympy]\n", "\u001b[2K    Found existing installation: torch 1.7.1━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/3\u001b[0m [sympy]\n", "\u001b[2K    Uninstalling torch-1.7.1:━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━\u001b[0m \u001b[32m2/3\u001b[0m [torch]\n", "\u001b[2K      Successfully uninstalled torch-1.7.10m\u001b[90m━━━━━━━━━━━━━\u001b[0m \u001b[32m2/3\u001b[0m [torch]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3/3\u001b[0m [torch]32m2/3\u001b[0m [torch]\n", "\u001b[1A\u001b[2KSuccessfully installed sympy-1.13.1 torch-2.6.0 triton-3.2.0\n"]}], "source": ["! pip install neuralforecast"]}, {"cell_type": "code", "execution_count": 2, "id": "f541f19e", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "/home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages/torch/lib/libtorch_cuda.so: undefined symbol: ncclCommRegister", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON>andas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mneuralforecast\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m NeuralForecast\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mneuralforecast\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m NBEATS, NH<PERSON><PERSON>, <PERSON><PERSON>\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mneuralforecast\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AirPassengersDF\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/neuralforecast/__init__.py:3\u001b[0m\n\u001b[1;32m      1\u001b[0m __version__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m3.0.1\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      2\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNeuralForecast\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m NeuralForecast\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON>mon\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_base_model\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m DistributedConfig  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/neuralforecast/core.py:16\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m---> 16\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpytorch_lightning\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpl\u001b[39;00m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mutilsforecast\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mprocessing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mufp\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/pytorch_lightning/__init__.py:25\u001b[0m\n\u001b[1;32m     22\u001b[0m     _logger\u001b[38;5;241m.\u001b[39maddHandler(logging\u001b[38;5;241m.\u001b[39mStreamHandler())\n\u001b[1;32m     23\u001b[0m     _logger\u001b[38;5;241m.\u001b[39mpropagate \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[0;32m---> 25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_fabric\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutilities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mseed\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m seed_everything  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     26\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_fabric\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutilities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mwarnings\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m disable_possible_user_warnings  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpytorch_lightning\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcallbacks\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Callback  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/lightning_fabric/__init__.py:35\u001b[0m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m sys\u001b[38;5;241m.\u001b[39mplatform \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwin32\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     32\u001b[0m     os\u001b[38;5;241m.\u001b[39menviron[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUSE_LIBUV\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m0\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_fabric\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfabric\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m <PERSON><PERSON><PERSON>  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_fabric\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutilities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mseed\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m seed_everything  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_fabric\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutilities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mwarnings\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m disable_possible_user_warnings  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/lightning_fabric/fabric.py:29\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Path\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     21\u001b[0m     Any,\n\u001b[1;32m     22\u001b[0m     Callable,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     26\u001b[0m     overload,\n\u001b[1;32m     27\u001b[0m )\n\u001b[0;32m---> 29\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnn\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnn\u001b[39;00m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlightning_utilities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapply_func\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m apply_to_collection\n", "File \u001b[0;32m~/anaconda3/envs/flood/lib/python3.9/site-packages/torch/__init__.py:405\u001b[0m\n\u001b[1;32m    403\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m USE_GLOBAL_DEPS:\n\u001b[1;32m    404\u001b[0m         _load_global_deps()\n\u001b[0;32m--> 405\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_C\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa: F403\u001b[39;00m\n\u001b[1;32m    408\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mSymInt\u001b[39;00m:\n\u001b[1;32m    409\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    410\u001b[0m \u001b[38;5;124;03m    Like an int (including magic methods), but redirects all operations on the\u001b[39;00m\n\u001b[1;32m    411\u001b[0m \u001b[38;5;124;03m    wrapped node. This is used in particular to symbolically record operations\u001b[39;00m\n\u001b[1;32m    412\u001b[0m \u001b[38;5;124;03m    in the symbolic shape workflow.\u001b[39;00m\n\u001b[1;32m    413\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: /home/<USER>/anaconda3/envs/flood/lib/python3.9/site-packages/torch/lib/libtorch_cuda.so: undefined symbol: ncclCommRegister"]}], "source": ["import pandas as pd\n", "from neuralforecast import NeuralForecast\n", "from neuralforecast.models import NBEATS, NHITS, MLP\n", "from neuralforecast.utils import AirPassengersDF\n", "\n", "# 使用示例数据\n", "df = AirPassengersDF\n", "print(df.head())"]}], "metadata": {"kernelspec": {"display_name": "flood", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}
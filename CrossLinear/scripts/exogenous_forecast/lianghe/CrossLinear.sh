export CUDA_VISIBLE_DEVICES=0

model_name=CrossLinear
des='CrossLinear-MS'

python -u run.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/ \
  --data_path lianghe.csv \
  --model_id linghe_168_12 \
  --model $model_name \
  --data custom \
  --features MS \
  --seq_len 168 \
  --label_len 96 \
  --pred_len 12 \
  --dec_in 2 \
  --patch_len 8 \
  --d_model 128 \
  --d_ff 1024 \
  --alpha 0.5 \
  --beta 1 \
  --des $des \
  --batch_size 16 \
  --learning_rate 0.0001 \
  --itr 1 

python -u run.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/ \
  --data_path lianghe.csv \
  --model_id linghe_168_24 \
  --model $model_name \
  --data custom \
  --features MS \
  --seq_len 168 \
  --label_len 96 \
  --pred_len 24 \
  --dec_in 2 \
  --patch_len 8 \
  --d_model 256 \
  --d_ff 1024 \
  --alpha 0.5 \
  --beta 1 \
  --des $des \
  --batch_size 16 \
  --learning_rate 0.0001 \
  --itr 1 
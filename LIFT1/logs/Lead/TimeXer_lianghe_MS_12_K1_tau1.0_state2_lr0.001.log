2025-07-16 20:15:15
Args in experiment:
Namespace(task_name='long_term_forecast', is_training=1, train_only=False, wo_test=False, only_test=False, model='TimeXer', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='lianghe', features='MS', batch_size=8, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=12, leader_num=1, state_num=2, prefetch_path='./prefetch/lianghe_L168_max', tag='_max', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=8, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, use_norm=1, embed_type=0, d_model=256, n_heads=8, e_layers=1, d_layers=1, d_ff=1024, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=1, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.001, des='test', loss='RMSE', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='lianghe.csv', dec_in=2, data='custom', model_id='lianghe_168_12_TimeXer', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Number of Params: 1167116 -> 1167697 (+581)
Trainable Params: 1167697 (100.0%)
Checkpoints in checkpoints/lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 6681
Loading prefetch files from ./prefetch/lianghe_L168_max_train.npz
val 970
Loading prefetch files from ./prefetch/lianghe_L168_max_val.npz
Epoch: 1, Steps: 835 | Train Loss: 4.5975872 Vali Loss: 0.0008072
Epoch: 1 cost time: 11.875787019729614
Updating learning rate to 0.001
Epoch: 2, Steps: 835 | Train Loss: 1.5075965 Vali Loss: 0.0005471
Epoch: 2 cost time: 10.010245084762573
Updating learning rate to 0.0005
Epoch: 3, Steps: 835 | Train Loss: 1.0636967 Vali Loss: 0.0005638
EarlyStopping counter: 1 out of 5
Epoch: 3 cost time: 10.038297176361084
Updating learning rate to 0.00025
Epoch: 4, Steps: 835 | Train Loss: 0.7267312 Vali Loss: 0.0003760
Epoch: 4 cost time: 10.066675901412964
Updating learning rate to 0.000125
Epoch: 5, Steps: 835 | Train Loss: 0.6022890 Vali Loss: 0.0003437
Epoch: 5 cost time: 10.090978622436523
Updating learning rate to 6.25e-05
Epoch: 6, Steps: 835 | Train Loss: 0.5433472 Vali Loss: 0.0003203
Epoch: 6 cost time: 9.772936344146729
Updating learning rate to 3.125e-05
Epoch: 7, Steps: 835 | Train Loss: 0.5103632 Vali Loss: 0.0003469
EarlyStopping counter: 1 out of 5
Epoch: 7 cost time: 9.75468373298645
Updating learning rate to 1.5625e-05
Epoch: 8, Steps: 835 | Train Loss: 0.5120375 Vali Loss: 0.0003389
EarlyStopping counter: 2 out of 5
Epoch: 8 cost time: 9.685844898223877
Updating learning rate to 7.8125e-06
Epoch: 9, Steps: 835 | Train Loss: 0.4892757 Vali Loss: 0.0003311
EarlyStopping counter: 3 out of 5
Epoch: 9 cost time: 9.820407390594482
Updating learning rate to 3.90625e-06
Epoch: 10, Steps: 835 | Train Loss: 0.5005656 Vali Loss: 0.0003294
EarlyStopping counter: 4 out of 5
Epoch: 10 cost time: 9.699590921401978
Updating learning rate to 1.953125e-06
Epoch: 11, Steps: 835 | Train Loss: 0.4658601 Vali Loss: 0.0003278
EarlyStopping counter: 5 out of 5
Epoch: 11 cost time: 9.715346574783325
Early stopping
Best Valid MSE: 0.00032030921172632545
>>>>>>>testing : lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 163
Loading prefetch files from ./prefetch/lianghe_L168_max_test.npz
RMSE:0.26208972930908203, NSE:0.24817109107971191, R:0.6877716779708862
{'corr': [0.6877717, 0.0],
 'nse': [0.24817109107971191, 0.0],
 'rmse': [0.26208973, 0.0]}

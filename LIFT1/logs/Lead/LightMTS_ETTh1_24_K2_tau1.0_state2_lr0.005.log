2025-07-15 00:33:25
Args in experiment:
Namespace(is_training=1, train_only=False, wo_test=False, only_test=False, model='LightMTS', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='ETTh1', features='MS', batch_size=32, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=336, label_len=48, pred_len=24, leader_num=2, state_num=2, prefetch_path='./prefetch/ETTh1_L336_naive', tag='_naive', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=False, pin_gpu=True, pretrain=False, freeze=False, lift=False, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=16, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, embed_type=0, d_model=512, n_heads=8, e_layers=2, d_layers=1, d_ff=2048, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=5, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.005, des='test', loss='mse', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=7, c_out=1, data_path='ETTh1.csv', dec_in=7, data='ETTh1', model_id='ETTh1_336_24_LightMTS', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Checkpoints in checkpoints/ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth
>>>>>>>start training : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0>>>>>>>>>>>>>>>>>>>>>>>>>>
train 8281
Loading prefetch files from ./prefetch/ETTh1_L336_naive_train.npz
Traceback (most recent call last):
  File "/home/<USER>/Flood_flow_prediction/Flow_Prediction/LIFT/data_provider/data_loader.py", line 544, in _load_prefetch_files
    assert prefetch['leader_ids' + suffix].shape[-1] >= self.K
AssertionError
Fail to load prefetch files

  0%|          | 0/21 [00:00<?, ?it/s]
100%|██████████| 21/21 [00:00<00:00, 801.09it/s]

  0%|          | 0/7 [00:00<?, ?it/s]
 29%|██▊       | 2/7 [00:00<00:00, 17.68it/s]
 57%|█████▋    | 4/7 [00:00<00:00, 12.16it/s]
 86%|████████▌ | 6/7 [00:00<00:00, 11.05it/s]
100%|██████████| 7/7 [00:00<00:00, 11.39it/s]
Generate new prefetch files to ./prefetch/ETTh1_L336_naive_train.npz
val 2857
Loading prefetch files from ./prefetch/ETTh1_L336_naive_val.npz
Traceback (most recent call last):
  File "/home/<USER>/Flood_flow_prediction/Flow_Prediction/LIFT/data_provider/data_loader.py", line 544, in _load_prefetch_files
    assert prefetch['leader_ids' + suffix].shape[-1] >= self.K
AssertionError
Fail to load prefetch files

  0%|          | 0/7 [00:00<?, ?it/s]
 57%|█████▋    | 4/7 [00:00<00:00, 31.22it/s]
100%|██████████| 7/7 [00:00<00:00, 28.63it/s]
Generate new prefetch files to ./prefetch/ETTh1_L336_naive_val.npz
Epoch: 1, Steps: 258 | Train Loss: 0.0933579 Vali Loss: 0.0550057
Epoch: 1 cost time: 2.240666389465332
Updating learning rate to 0.005
Epoch: 2, Steps: 258 | Train Loss: 0.0778981 Vali Loss: 0.0528016
Epoch: 2 cost time: 1.417876958847046
Updating learning rate to 0.0025
Epoch: 3, Steps: 258 | Train Loss: 0.0711776 Vali Loss: 0.0533386
EarlyStopping counter: 1 out of 5
Epoch: 3 cost time: 1.387293815612793
Updating learning rate to 0.00125
Epoch: 4, Steps: 258 | Train Loss: 0.0683327 Vali Loss: 0.0516589
Epoch: 4 cost time: 1.4077880382537842
Updating learning rate to 0.000625
Epoch: 5, Steps: 258 | Train Loss: 0.0669683 Vali Loss: 0.0520940
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4051167964935303
Updating learning rate to 0.0003125
Epoch: 6, Steps: 258 | Train Loss: 0.0661218 Vali Loss: 0.0521232
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.3467459678649902
Updating learning rate to 0.00015625
Epoch: 7, Steps: 258 | Train Loss: 0.0657943 Vali Loss: 0.0522479
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.3595092296600342
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 258 | Train Loss: 0.0654455 Vali Loss: 0.0523290
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.3667309284210205
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 258 | Train Loss: 0.0652795 Vali Loss: 0.0522795
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.3718843460083008
Early stopping
Best Valid MSE: 0.05165889836178067
>>>>>>>testing : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 2857
Loading prefetch files from ./prefetch/ETTh1_L336_naive_test.npz
Traceback (most recent call last):
  File "/home/<USER>/Flood_flow_prediction/Flow_Prediction/LIFT/data_provider/data_loader.py", line 544, in _load_prefetch_files
    assert prefetch['leader_ids' + suffix].shape[-1] >= self.K
AssertionError
Fail to load prefetch files

  0%|          | 0/7 [00:00<?, ?it/s]
 57%|█████▋    | 4/7 [00:00<00:00, 30.88it/s]
100%|██████████| 7/7 [00:00<00:00, 28.43it/s]
Generate new prefetch files to ./prefetch/ETTh1_L336_naive_test.npz
mse:0.02803076526384595, mae:0.12700415453155878
Seed: 2024
Use GPU: cuda:0
Checkpoints in checkpoints/ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1/checkpoint.pth
>>>>>>>start training : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 258 | Train Loss: 0.0910188 Vali Loss: 0.0556166
Epoch: 1 cost time: 1.3940911293029785
Updating learning rate to 0.005
Epoch: 2, Steps: 258 | Train Loss: 0.0768925 Vali Loss: 0.0549780
Epoch: 2 cost time: 1.4219231605529785
Updating learning rate to 0.0025
Epoch: 3, Steps: 258 | Train Loss: 0.0716623 Vali Loss: 0.0543394
Epoch: 3 cost time: 1.405078411102295
Updating learning rate to 0.00125
Epoch: 4, Steps: 258 | Train Loss: 0.0690164 Vali Loss: 0.0541583
Epoch: 4 cost time: 1.3992986679077148
Updating learning rate to 0.000625
Epoch: 5, Steps: 258 | Train Loss: 0.0673834 Vali Loss: 0.0549146
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4000530242919922
Updating learning rate to 0.0003125
Epoch: 6, Steps: 258 | Train Loss: 0.0664260 Vali Loss: 0.0548233
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.397923231124878
Updating learning rate to 0.00015625
Epoch: 7, Steps: 258 | Train Loss: 0.0660649 Vali Loss: 0.0545781
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.4100964069366455
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 258 | Train Loss: 0.0658171 Vali Loss: 0.0547457
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.4530596733093262
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 258 | Train Loss: 0.0657056 Vali Loss: 0.0548080
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.3812205791473389
Early stopping
Best Valid MSE: 0.054158251900016594
>>>>>>>testing : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.029215298021928723, mae:0.1311584097016944
Seed: 2025
Use GPU: cuda:0
Checkpoints in checkpoints/ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2/checkpoint.pth
>>>>>>>start training : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 258 | Train Loss: 0.0926154 Vali Loss: 0.0527102
Epoch: 1 cost time: 1.438300609588623
Updating learning rate to 0.005
Epoch: 2, Steps: 258 | Train Loss: 0.0785999 Vali Loss: 0.0514818
Epoch: 2 cost time: 1.4395763874053955
Updating learning rate to 0.0025
Epoch: 3, Steps: 258 | Train Loss: 0.0730947 Vali Loss: 0.0512842
Epoch: 3 cost time: 1.4357330799102783
Updating learning rate to 0.00125
Epoch: 4, Steps: 258 | Train Loss: 0.0706110 Vali Loss: 0.0505757
Epoch: 4 cost time: 1.442854881286621
Updating learning rate to 0.000625
Epoch: 5, Steps: 258 | Train Loss: 0.0694162 Vali Loss: 0.0506181
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4221627712249756
Updating learning rate to 0.0003125
Epoch: 6, Steps: 258 | Train Loss: 0.0686641 Vali Loss: 0.0508958
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.431342363357544
Updating learning rate to 0.00015625
Epoch: 7, Steps: 258 | Train Loss: 0.0682181 Vali Loss: 0.0506789
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.449244499206543
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 258 | Train Loss: 0.0680427 Vali Loss: 0.0506456
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.4043524265289307
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 258 | Train Loss: 0.0678150 Vali Loss: 0.0507894
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.3975470066070557
Early stopping
Best Valid MSE: 0.05057574691397421
>>>>>>>testing : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.027159914323933384, mae:0.1261926011101199
Seed: 2026
Use GPU: cuda:0
Checkpoints in checkpoints/ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3/checkpoint.pth
>>>>>>>start training : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 258 | Train Loss: 0.0915339 Vali Loss: 0.0603151
Epoch: 1 cost time: 1.4130470752716064
Updating learning rate to 0.005
Epoch: 2, Steps: 258 | Train Loss: 0.0766799 Vali Loss: 0.0553502
Epoch: 2 cost time: 1.4265358448028564
Updating learning rate to 0.0025
Epoch: 3, Steps: 258 | Train Loss: 0.0703115 Vali Loss: 0.0547879
Epoch: 3 cost time: 1.4353532791137695
Updating learning rate to 0.00125
Epoch: 4, Steps: 258 | Train Loss: 0.0675506 Vali Loss: 0.0556586
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.3904411792755127
Updating learning rate to 0.000625
Epoch: 5, Steps: 258 | Train Loss: 0.0662638 Vali Loss: 0.0549145
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.3935372829437256
Updating learning rate to 0.0003125
Epoch: 6, Steps: 258 | Train Loss: 0.0655336 Vali Loss: 0.0548709
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.4298458099365234
Updating learning rate to 0.00015625
Epoch: 7, Steps: 258 | Train Loss: 0.0650349 Vali Loss: 0.0552638
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.424912929534912
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 258 | Train Loss: 0.0647799 Vali Loss: 0.0551036
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.4200248718261719
Early stopping
Best Valid MSE: 0.05478791750214073
>>>>>>>testing : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.028819843537330962, mae:0.1273984168571959
Seed: 2027
Use GPU: cuda:0
Checkpoints in checkpoints/ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4/checkpoint.pth
>>>>>>>start training : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 258 | Train Loss: 0.0909265 Vali Loss: 0.0535424
Epoch: 1 cost time: 1.414198637008667
Updating learning rate to 0.005
Epoch: 2, Steps: 258 | Train Loss: 0.0786558 Vali Loss: 0.0506299
Epoch: 2 cost time: 1.413851022720337
Updating learning rate to 0.0025
Epoch: 3, Steps: 258 | Train Loss: 0.0720125 Vali Loss: 0.0507135
EarlyStopping counter: 1 out of 5
Epoch: 3 cost time: 1.4099979400634766
Updating learning rate to 0.00125
Epoch: 4, Steps: 258 | Train Loss: 0.0687410 Vali Loss: 0.0491761
Epoch: 4 cost time: 1.4741544723510742
Updating learning rate to 0.000625
Epoch: 5, Steps: 258 | Train Loss: 0.0670439 Vali Loss: 0.0507985
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.401120901107788
Updating learning rate to 0.0003125
Epoch: 6, Steps: 258 | Train Loss: 0.0660790 Vali Loss: 0.0508612
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.4694490432739258
Updating learning rate to 0.00015625
Epoch: 7, Steps: 258 | Train Loss: 0.0655556 Vali Loss: 0.0509206
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.3690998554229736
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 258 | Train Loss: 0.0653336 Vali Loss: 0.0509921
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.347426176071167
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 258 | Train Loss: 0.0651821 Vali Loss: 0.0509003
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.373049020767212
Early stopping
Best Valid MSE: 0.049176108824570526
>>>>>>>testing : ETTh1_336_24_LightMTS_ETTh1_ftMS_sl336_ll48_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.028169779582252277, mae:0.12778403258020465
{'mae': [0.12790752295615473, 0.0017087852395101819],
 'mse': [0.02827912014585826, 0.0007064089438602974]}

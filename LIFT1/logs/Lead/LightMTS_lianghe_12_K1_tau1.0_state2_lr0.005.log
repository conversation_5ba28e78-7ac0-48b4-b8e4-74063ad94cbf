2025-07-15 02:26:01
Args in experiment:
Namespace(is_training=1, train_only=False, wo_test=False, only_test=False, model='LightMTS', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='lianghe', features='MS', batch_size=32, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=12, leader_num=1, state_num=2, prefetch_path='./prefetch/lianghe_L168_max', tag='_max', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=16, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, embed_type=0, d_model=512, n_heads=8, e_layers=2, d_layers=1, d_ff=2048, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=5, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.005, des='test', loss='mse', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='lianghe.csv', dec_in=2, data='custom', model_id='lianghe_168_12_LightMTS', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 9622
Loading prefetch files from ./prefetch/lianghe_L168_max_train.npz
val 970
Loading prefetch files from ./prefetch/lianghe_L168_max_val.npz
Epoch: 1, Steps: 300 | Train Loss: 0.8864575 Vali Loss: 0.0005734
Epoch: 1 cost time: 2.334601640701294
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.6186589 Vali Loss: 0.0005707
Epoch: 2 cost time: 1.4598126411437988
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.5062480 Vali Loss: 0.0004136
Epoch: 3 cost time: 1.44053316116333
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.4768720 Vali Loss: 0.0004337
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.4544837474822998
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.4404497 Vali Loss: 0.0004544
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.446310043334961
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.4348382 Vali Loss: 0.0004478
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.4595959186553955
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.4314573 Vali Loss: 0.0004496
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.4592220783233643
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.4298085 Vali Loss: 0.0004559
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.4670562744140625
Early stopping
Best Valid MSE: 0.00041358632709792196
>>>>>>>testing : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 1949
Loading prefetch files from ./prefetch/lianghe_L168_max_test.npz
mse:0.11819775770093667, mae:0.05234013742423066
Seed: 2024
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 0.9732165 Vali Loss: 0.0004577
Epoch: 1 cost time: 1.457500696182251
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.7286583 Vali Loss: 0.0004478
Epoch: 2 cost time: 1.4865326881408691
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.6072851 Vali Loss: 0.0003458
Epoch: 3 cost time: 1.5142991542816162
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.5471225 Vali Loss: 0.0003689
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.4518828392028809
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.5266929 Vali Loss: 0.0003652
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.4515612125396729
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.5160824 Vali Loss: 0.0003608
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.4749810695648193
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.5117399 Vali Loss: 0.0003548
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.4269440174102783
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.5087953 Vali Loss: 0.0003556
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.486548662185669
Early stopping
Best Valid MSE: 0.0003457743017255173
>>>>>>>testing : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.08782912639666356, mae:0.048582909614094935
Seed: 2025
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.0016122 Vali Loss: 0.0003514
Epoch: 1 cost time: 1.4627516269683838
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.7064453 Vali Loss: 0.0006064
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.4648776054382324
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.5727287 Vali Loss: 0.0004046
EarlyStopping counter: 2 out of 5
Epoch: 3 cost time: 1.4379308223724365
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.4897903 Vali Loss: 0.0004213
EarlyStopping counter: 3 out of 5
Epoch: 4 cost time: 1.449049711227417
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.4555273 Vali Loss: 0.0004078
EarlyStopping counter: 4 out of 5
Epoch: 5 cost time: 1.4992668628692627
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.4430654 Vali Loss: 0.0004209
EarlyStopping counter: 5 out of 5
Epoch: 6 cost time: 1.4659206867218018
Early stopping
Best Valid MSE: 0.0003513523105842372
>>>>>>>testing : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.09430996482356425, mae:0.052847806609910904
Seed: 2026
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 0.9681003 Vali Loss: 0.0004999
Epoch: 1 cost time: 1.4659059047698975
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.6762119 Vali Loss: 0.0005304
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.4495413303375244
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.5521984 Vali Loss: 0.0004593
Epoch: 3 cost time: 1.4626121520996094
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.4557176 Vali Loss: 0.0004769
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.446723222732544
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.4554968 Vali Loss: 0.0004668
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.4448151588439941
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.4479416 Vali Loss: 0.0004747
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.462165117263794
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.4433924 Vali Loss: 0.0004680
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.4981887340545654
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.4418304 Vali Loss: 0.0004792
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.4461698532104492
Early stopping
Best Valid MSE: 0.0004592933391298478
>>>>>>>testing : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.09688309712224456, mae:0.05401676678553807
Seed: 2027
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 0.9220923 Vali Loss: 0.0003957
Epoch: 1 cost time: 1.4991209506988525
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.7280926 Vali Loss: 0.0005850
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.5271596908569336
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.6012898 Vali Loss: 0.0003920
Epoch: 3 cost time: 1.4457039833068848
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.5506905 Vali Loss: 0.0003706
Epoch: 4 cost time: 1.4563384056091309
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.5232776 Vali Loss: 0.0003804
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4595251083374023
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.5102671 Vali Loss: 0.0003883
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.4461774826049805
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.4914678 Vali Loss: 0.0003854
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.4738261699676514
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.4986281 Vali Loss: 0.0003878
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.4385082721710205
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.4963991 Vali Loss: 0.0003831
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.478193998336792
Early stopping
Best Valid MSE: 0.00037061482932282766
>>>>>>>testing : lianghe_168_12_LightMTS_custom_ftMS_sl168_ll96_pl12_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.09113486724922103, mae:0.04984041581311673
{'mae': [0.051525607249378255, 0.0020058357828540824],
 'mse': [0.09767096265852601, 0.010703724935440567]}

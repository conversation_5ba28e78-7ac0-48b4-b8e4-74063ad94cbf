2025-07-15 02:35:57
Args in experiment:
Namespace(is_training=1, train_only=False, wo_test=False, only_test=False, model='DLinear', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='./checkpoints/', root_path='./dataset/', dataset='lianghe', features='MS', batch_size=32, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=24, leader_num=1, state_num=2, prefetch_path='./prefetch/lianghe_L168_max', tag='_max', prefetch_batch_size=128, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=16, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, embed_type=0, d_model=512, n_heads=8, e_layers=2, d_layers=1, d_ff=2048, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=5, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.005, des='test', loss='mse', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='lianghe.csv', dec_in=2, data='custom', model_id='lianghe_168_24_DLinear', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Number of Params: 8112 -> 9131 (+1019)
Trainable Params: 9131 (112.6%)
Checkpoints in checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 9610
Loading prefetch files from ./prefetch/lianghe_L168_max_train.npz
val 958
Loading prefetch files from ./prefetch/lianghe_L168_max_val.npz
Epoch: 1, Steps: 300 | Train Loss: 1.1715352 Vali Loss: 0.0027899
Epoch: 1 cost time: 2.5204708576202393
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8653029 Vali Loss: 0.0032261
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.663151502609253
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7884438 Vali Loss: 0.0047226
EarlyStopping counter: 2 out of 5
Epoch: 3 cost time: 1.728733777999878
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7563105 Vali Loss: 0.0033303
EarlyStopping counter: 3 out of 5
Epoch: 4 cost time: 1.6324377059936523
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7439722 Vali Loss: 0.0033265
EarlyStopping counter: 4 out of 5
Epoch: 5 cost time: 1.6045565605163574
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7398909 Vali Loss: 0.0036869
EarlyStopping counter: 5 out of 5
Epoch: 6 cost time: 1.6506288051605225
Early stopping
Best Valid MSE: 0.0027899166633342876
Save checkpoint to ./checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift
>>>>>>>testing : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 1937
Loading prefetch files from ./prefetch/lianghe_L168_max_test.npz
mse:0.11949673080901807, mae:0.09500002971800812
Seed: 2024
Use GPU: cuda:0
Number of Params: 8112 -> 9131 (+1019)
Trainable Params: 9131 (112.6%)
Checkpoints in checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.0967316 Vali Loss: 0.0143587
Epoch: 1 cost time: 1.6080763339996338
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8564040 Vali Loss: 0.0029007
Epoch: 2 cost time: 1.6049273014068604
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7238090 Vali Loss: 0.0066172
EarlyStopping counter: 1 out of 5
Epoch: 3 cost time: 1.6309690475463867
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7013904 Vali Loss: 0.0049298
EarlyStopping counter: 2 out of 5
Epoch: 4 cost time: 1.632662296295166
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.6907812 Vali Loss: 0.0037094
EarlyStopping counter: 3 out of 5
Epoch: 5 cost time: 1.6270568370819092
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.6474709 Vali Loss: 0.0029048
EarlyStopping counter: 4 out of 5
Epoch: 6 cost time: 1.644390344619751
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.6797553 Vali Loss: 0.0031904
EarlyStopping counter: 5 out of 5
Epoch: 7 cost time: 1.606645107269287
Early stopping
Best Valid MSE: 0.002900691831420208
Save checkpoint to ./checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift
>>>>>>>testing : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.11467478264682861, mae:0.08376741799075721
Seed: 2025
Use GPU: cuda:0
Number of Params: 8112 -> 9131 (+1019)
Trainable Params: 9131 (112.6%)
Checkpoints in checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1787177 Vali Loss: 0.0110447
Epoch: 1 cost time: 1.6481800079345703
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.9253584 Vali Loss: 0.0075475
Epoch: 2 cost time: 1.6872100830078125
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7901901 Vali Loss: 0.0037949
Epoch: 3 cost time: 1.6028931140899658
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7490491 Vali Loss: 0.0044720
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.6331229209899902
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7388908 Vali Loss: 0.0052235
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.643519401550293
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7339902 Vali Loss: 0.0045988
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.659776210784912
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7308700 Vali Loss: 0.0042678
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.6116693019866943
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.7294489 Vali Loss: 0.0044930
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.689457893371582
Early stopping
Best Valid MSE: 0.0037949206066671117
Save checkpoint to ./checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift
>>>>>>>testing : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.11681472215717496, mae:0.09756299487655369
Seed: 2026
Use GPU: cuda:0
Number of Params: 8112 -> 9131 (+1019)
Trainable Params: 9131 (112.6%)
Checkpoints in checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1897162 Vali Loss: 0.0054264
Epoch: 1 cost time: 1.6415824890136719
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8932052 Vali Loss: 0.0045614
Epoch: 2 cost time: 1.610400676727295
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7575635 Vali Loss: 0.0032067
Epoch: 3 cost time: 1.666813850402832
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7172703 Vali Loss: 0.0036624
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.6272075176239014
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7056671 Vali Loss: 0.0039316
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.654512643814087
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7016405 Vali Loss: 0.0038226
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.625715970993042
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.6981995 Vali Loss: 0.0040672
EarlyStopping counter: 4 out of 5
Epoch: 7 cost time: 1.6844170093536377
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.6980770 Vali Loss: 0.0040429
EarlyStopping counter: 5 out of 5
Epoch: 8 cost time: 1.6392714977264404
Early stopping
Best Valid MSE: 0.0032067161278221115
Save checkpoint to ./checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift
>>>>>>>testing : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.11138653709799835, mae:0.08508246483685453
Seed: 2027
Use GPU: cuda:0
Number of Params: 8112 -> 9131 (+1019)
Trainable Params: 9131 (112.6%)
Checkpoints in checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1456500 Vali Loss: 0.0029915
Epoch: 1 cost time: 1.6598536968231201
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8767959 Vali Loss: 0.0051678
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.7170069217681885
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7297621 Vali Loss: 0.0051507
EarlyStopping counter: 2 out of 5
Epoch: 3 cost time: 1.7648043632507324
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.6989706 Vali Loss: 0.0032461
EarlyStopping counter: 3 out of 5
Epoch: 4 cost time: 1.7593474388122559
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.6963923 Vali Loss: 0.0037369
EarlyStopping counter: 4 out of 5
Epoch: 5 cost time: 1.6897814273834229
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.6915098 Vali Loss: 0.0040743
EarlyStopping counter: 5 out of 5
Epoch: 6 cost time: 1.6344332695007324
Early stopping
Best Valid MSE: 0.00299153383821249
Save checkpoint to ./checkpoints/lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift
>>>>>>>testing : lianghe_168_24_DLinear_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.12531534543142653, mae:0.09059106985645889
{'mae': [0.09040079545572649, 0.0053809403983878165],
 'mse': [0.1175376236284893, 0.004709254036846517]}

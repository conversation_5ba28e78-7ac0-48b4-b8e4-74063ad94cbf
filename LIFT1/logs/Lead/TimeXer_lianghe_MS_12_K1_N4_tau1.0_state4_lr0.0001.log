2025-07-16 19:53:40
Args in experiment:
Namespace(task_name='long_term_forecast', is_training=1, train_only=False, wo_test=False, only_test=False, model='TimeXer', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='lianghe', features='MS', batch_size=8, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=12, leader_num=1, state_num=4, prefetch_path='./prefetch/lianghe_L168_max', tag='_max', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=8, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, use_norm=1, embed_type=0, d_model=256, n_heads=8, e_layers=1, d_layers=1, d_ff=1024, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=1, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.0001, des='test', loss='RMSE', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='lianghe.csv', dec_in=2, data='custom', model_id='lianghe_168_12_TimeXer', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Number of Params: 1167116 -> 1168123 (+1007)
Trainable Params: 1168123 (100.1%)
Checkpoints in checkpoints/lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.0001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.0001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 6681
Loading prefetch files from ./prefetch/lianghe_L168_max_train.npz
val 970
Loading prefetch files from ./prefetch/lianghe_L168_max_val.npz
Epoch: 1, Steps: 835 | Train Loss: 1.3537715 Vali Loss: 0.0004968
Epoch: 1 cost time: 12.942524433135986
Updating learning rate to 0.0001
Epoch: 2, Steps: 835 | Train Loss: 0.8714878 Vali Loss: 0.0004188
Epoch: 2 cost time: 11.265305042266846
Updating learning rate to 5e-05
Epoch: 3, Steps: 835 | Train Loss: 0.6291894 Vali Loss: 0.0003746
Epoch: 3 cost time: 11.085346460342407
Updating learning rate to 2.5e-05
Epoch: 4, Steps: 835 | Train Loss: 0.5656051 Vali Loss: 0.0003684
Epoch: 4 cost time: 11.223571300506592
Updating learning rate to 1.25e-05
Epoch: 5, Steps: 835 | Train Loss: 0.4969227 Vali Loss: 0.0003549
Epoch: 5 cost time: 11.222362756729126
Updating learning rate to 6.25e-06
Epoch: 6, Steps: 835 | Train Loss: 0.4996678 Vali Loss: 0.0003509
Epoch: 6 cost time: 10.202436685562134
Updating learning rate to 3.125e-06
Epoch: 7, Steps: 835 | Train Loss: 0.4781669 Vali Loss: 0.0003602
EarlyStopping counter: 1 out of 5
Epoch: 7 cost time: 9.146597385406494
Updating learning rate to 1.5625e-06
Epoch: 8, Steps: 835 | Train Loss: 0.4619917 Vali Loss: 0.0003522
EarlyStopping counter: 2 out of 5
Epoch: 8 cost time: 9.184446334838867
Updating learning rate to 7.8125e-07
Epoch: 9, Steps: 835 | Train Loss: 0.4841496 Vali Loss: 0.0003567
EarlyStopping counter: 3 out of 5
Epoch: 9 cost time: 9.085904836654663
Updating learning rate to 3.90625e-07
Epoch: 10, Steps: 835 | Train Loss: 0.4802864 Vali Loss: 0.0003548
EarlyStopping counter: 4 out of 5
Epoch: 10 cost time: 9.215267181396484
Updating learning rate to 1.953125e-07
Epoch: 11, Steps: 835 | Train Loss: 0.4807770 Vali Loss: 0.0003547
EarlyStopping counter: 5 out of 5
Epoch: 11 cost time: 10.037031173706055
Early stopping
Best Valid MSE: 0.00035089469528136474
>>>>>>>testing : lianghe_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.0001_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 163
Loading prefetch files from ./prefetch/lianghe_L168_max_test.npz
RMSE:0.28191086649894714, NSE:0.13015347719192505, R:0.6762369871139526
{'corr': [0.676237, 0.0],
 'nse': [0.13015347719192505, 0.0],
 'rmse': [0.28191087, 0.0]}

2025-07-15 02:27:04
Args in experiment:
Namespace(is_training=1, train_only=False, wo_test=False, only_test=False, model='LightMTS', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='lianghe', features='MS', batch_size=32, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=24, leader_num=1, state_num=2, prefetch_path='./prefetch/lianghe_L168_max', tag='_max', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=16, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, embed_type=0, d_model=512, n_heads=8, e_layers=2, d_layers=1, d_ff=2048, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=5, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.005, des='test', loss='mse', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='lianghe.csv', dec_in=2, data='custom', model_id='lianghe_168_24_LightMTS', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 9610
Loading prefetch files from ./prefetch/lianghe_L168_max_train.npz
val 958
Loading prefetch files from ./prefetch/lianghe_L168_max_val.npz
Epoch: 1, Steps: 300 | Train Loss: 1.1492330 Vali Loss: 0.0005922
Epoch: 1 cost time: 2.349277973175049
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.9091978 Vali Loss: 0.0007805
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.4963710308074951
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.8023095 Vali Loss: 0.0005986
EarlyStopping counter: 2 out of 5
Epoch: 3 cost time: 1.5255694389343262
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7370496 Vali Loss: 0.0005468
Epoch: 4 cost time: 1.4986493587493896
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7236153 Vali Loss: 0.0005845
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4765384197235107
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7166419 Vali Loss: 0.0005611
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.480290412902832
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7127647 Vali Loss: 0.0005708
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.4936778545379639
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.7124276 Vali Loss: 0.0005733
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.4922258853912354
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.7115881 Vali Loss: 0.0005441
Epoch: 9 cost time: 1.4725141525268555
Updating learning rate to 1.953125e-05
Epoch: 10, Steps: 300 | Train Loss: 0.7103155 Vali Loss: 0.0005704
EarlyStopping counter: 1 out of 5
Epoch: 10 cost time: 1.50172758102417
Updating learning rate to 9.765625e-06
Epoch: 11, Steps: 300 | Train Loss: 0.7109925 Vali Loss: 0.0005601
EarlyStopping counter: 2 out of 5
Epoch: 11 cost time: 1.4915506839752197
Updating learning rate to 4.8828125e-06
Epoch: 12, Steps: 300 | Train Loss: 0.7108667 Vali Loss: 0.0005644
EarlyStopping counter: 3 out of 5
Epoch: 12 cost time: 1.4835937023162842
Updating learning rate to 2.44140625e-06
Epoch: 13, Steps: 300 | Train Loss: 0.7108001 Vali Loss: 0.0005726
EarlyStopping counter: 4 out of 5
Epoch: 13 cost time: 1.5207414627075195
Updating learning rate to 1.220703125e-06
Epoch: 14, Steps: 300 | Train Loss: 0.7107804 Vali Loss: 0.0005626
EarlyStopping counter: 5 out of 5
Epoch: 14 cost time: 1.4582183361053467
Early stopping
Best Valid MSE: 0.0005440896894814896
>>>>>>>testing : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 1937
Loading prefetch files from ./prefetch/lianghe_L168_max_test.npz
mse:0.11460748277835399, mae:0.054617077739810886
Seed: 2024
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1466241 Vali Loss: 0.0007330
Epoch: 1 cost time: 1.5169799327850342
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.9329372 Vali Loss: 0.0008819
EarlyStopping counter: 1 out of 5
Epoch: 2 cost time: 1.4950273036956787
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7786820 Vali Loss: 0.0006654
Epoch: 3 cost time: 1.4887917041778564
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7278617 Vali Loss: 0.0006982
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.4743309020996094
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7148790 Vali Loss: 0.0006478
Epoch: 5 cost time: 1.545398473739624
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7095643 Vali Loss: 0.0006409
Epoch: 6 cost time: 1.551321029663086
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7067549 Vali Loss: 0.0006400
Epoch: 7 cost time: 1.4890403747558594
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.7056065 Vali Loss: 0.0006242
Epoch: 8 cost time: 1.480285406112671
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.7048786 Vali Loss: 0.0006318
EarlyStopping counter: 1 out of 5
Epoch: 9 cost time: 1.5372827053070068
Updating learning rate to 1.953125e-05
Epoch: 10, Steps: 300 | Train Loss: 0.7045241 Vali Loss: 0.0006327
EarlyStopping counter: 2 out of 5
Epoch: 10 cost time: 1.4808435440063477
Updating learning rate to 9.765625e-06
Epoch: 11, Steps: 300 | Train Loss: 0.7043520 Vali Loss: 0.0006241
Epoch: 11 cost time: 1.5103988647460938
Updating learning rate to 4.8828125e-06
Epoch: 12, Steps: 300 | Train Loss: 0.7042488 Vali Loss: 0.0006270
EarlyStopping counter: 1 out of 5
Epoch: 12 cost time: 1.5286681652069092
Updating learning rate to 2.44140625e-06
Epoch: 13, Steps: 300 | Train Loss: 0.7041995 Vali Loss: 0.0006257
EarlyStopping counter: 2 out of 5
Epoch: 13 cost time: 1.5062222480773926
Updating learning rate to 1.220703125e-06
Epoch: 14, Steps: 300 | Train Loss: 0.7041694 Vali Loss: 0.0006405
EarlyStopping counter: 3 out of 5
Epoch: 14 cost time: 1.4853270053863525
Updating learning rate to 6.103515625e-07
Epoch: 15, Steps: 300 | Train Loss: 0.7041479 Vali Loss: 0.0006142
Epoch: 15 cost time: 1.4719231128692627
Updating learning rate to 3.0517578125e-07
Epoch: 16, Steps: 300 | Train Loss: 0.7041441 Vali Loss: 0.0006398
EarlyStopping counter: 1 out of 5
Epoch: 16 cost time: 1.4695026874542236
Updating learning rate to 1.52587890625e-07
Epoch: 17, Steps: 300 | Train Loss: 0.7041267 Vali Loss: 0.0006429
EarlyStopping counter: 2 out of 5
Epoch: 17 cost time: 1.5137746334075928
Updating learning rate to 7.62939453125e-08
Epoch: 18, Steps: 300 | Train Loss: 0.7040861 Vali Loss: 0.0006188
EarlyStopping counter: 3 out of 5
Epoch: 18 cost time: 1.492762804031372
Updating learning rate to 3.814697265625e-08
Epoch: 19, Steps: 300 | Train Loss: 0.7041364 Vali Loss: 0.0006441
EarlyStopping counter: 4 out of 5
Epoch: 19 cost time: 1.4835455417633057
Updating learning rate to 1.9073486328125e-08
Epoch: 20, Steps: 300 | Train Loss: 0.7040838 Vali Loss: 0.0006282
EarlyStopping counter: 5 out of 5
Epoch: 20 cost time: 1.5065686702728271
Early stopping
Best Valid MSE: 0.0006142017604173001
>>>>>>>testing : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_1_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.12205917673291491, mae:0.058142726804027045
Seed: 2025
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1116671 Vali Loss: 0.0006361
Epoch: 1 cost time: 1.5442695617675781
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8011519 Vali Loss: 0.0005728
Epoch: 2 cost time: 1.528425693511963
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7736958 Vali Loss: 0.0005514
Epoch: 3 cost time: 1.5171616077423096
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7192952 Vali Loss: 0.0005625
EarlyStopping counter: 1 out of 5
Epoch: 4 cost time: 1.470947504043579
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7069241 Vali Loss: 0.0005544
EarlyStopping counter: 2 out of 5
Epoch: 5 cost time: 1.5146102905273438
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7028021 Vali Loss: 0.0005623
EarlyStopping counter: 3 out of 5
Epoch: 6 cost time: 1.4669435024261475
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7008837 Vali Loss: 0.0005477
Epoch: 7 cost time: 1.4930980205535889
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.6998875 Vali Loss: 0.0005472
Epoch: 8 cost time: 1.5064122676849365
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.6938673 Vali Loss: 0.0005606
EarlyStopping counter: 1 out of 5
Epoch: 9 cost time: 1.5199637413024902
Updating learning rate to 1.953125e-05
Epoch: 10, Steps: 300 | Train Loss: 0.6990583 Vali Loss: 0.0005553
EarlyStopping counter: 2 out of 5
Epoch: 10 cost time: 1.4964594841003418
Updating learning rate to 9.765625e-06
Epoch: 11, Steps: 300 | Train Loss: 0.6989054 Vali Loss: 0.0005641
EarlyStopping counter: 3 out of 5
Epoch: 11 cost time: 1.43770432472229
Updating learning rate to 4.8828125e-06
Epoch: 12, Steps: 300 | Train Loss: 0.6988312 Vali Loss: 0.0005618
EarlyStopping counter: 4 out of 5
Epoch: 12 cost time: 1.4703185558319092
Updating learning rate to 2.44140625e-06
Epoch: 13, Steps: 300 | Train Loss: 0.6987858 Vali Loss: 0.0005516
EarlyStopping counter: 5 out of 5
Epoch: 13 cost time: 1.4195773601531982
Early stopping
Best Valid MSE: 0.0005472175057391347
>>>>>>>testing : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_2_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.11320561817854943, mae:0.05443397527257683
Seed: 2026
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.0998701 Vali Loss: 0.0007159
Epoch: 1 cost time: 1.4027485847473145
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8660123 Vali Loss: 0.0005983
Epoch: 2 cost time: 1.4649426937103271
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7786164 Vali Loss: 0.0005477
Epoch: 3 cost time: 1.41410493850708
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7298999 Vali Loss: 0.0005424
Epoch: 4 cost time: 1.4175026416778564
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7137545 Vali Loss: 0.0005618
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4547152519226074
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7086527 Vali Loss: 0.0005539
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.4131484031677246
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7059675 Vali Loss: 0.0005601
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.4070210456848145
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.7045738 Vali Loss: 0.0005527
EarlyStopping counter: 4 out of 5
Epoch: 8 cost time: 1.4193148612976074
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.7039393 Vali Loss: 0.0005564
EarlyStopping counter: 5 out of 5
Epoch: 9 cost time: 1.4069671630859375
Early stopping
Best Valid MSE: 0.0005423597271899404
>>>>>>>testing : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_3_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.1170224273139106, mae:0.05723938262697506
Seed: 2027
Use GPU: cuda:0
Checkpoints in checkpoints/lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift/checkpoint.pth
>>>>>>>start training : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
Epoch: 1, Steps: 300 | Train Loss: 1.1331888 Vali Loss: 0.0006575
Epoch: 1 cost time: 1.4122827053070068
Updating learning rate to 0.005
Epoch: 2, Steps: 300 | Train Loss: 0.8796169 Vali Loss: 0.0006083
Epoch: 2 cost time: 1.4230034351348877
Updating learning rate to 0.0025
Epoch: 3, Steps: 300 | Train Loss: 0.7709189 Vali Loss: 0.0005779
Epoch: 3 cost time: 1.4349160194396973
Updating learning rate to 0.00125
Epoch: 4, Steps: 300 | Train Loss: 0.7292825 Vali Loss: 0.0005768
Epoch: 4 cost time: 1.4272022247314453
Updating learning rate to 0.000625
Epoch: 5, Steps: 300 | Train Loss: 0.7150604 Vali Loss: 0.0005813
EarlyStopping counter: 1 out of 5
Epoch: 5 cost time: 1.4515495300292969
Updating learning rate to 0.0003125
Epoch: 6, Steps: 300 | Train Loss: 0.7105013 Vali Loss: 0.0005843
EarlyStopping counter: 2 out of 5
Epoch: 6 cost time: 1.4426162242889404
Updating learning rate to 0.00015625
Epoch: 7, Steps: 300 | Train Loss: 0.7079218 Vali Loss: 0.0005793
EarlyStopping counter: 3 out of 5
Epoch: 7 cost time: 1.4534432888031006
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 300 | Train Loss: 0.7068185 Vali Loss: 0.0005755
Epoch: 8 cost time: 1.4899897575378418
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 300 | Train Loss: 0.7061260 Vali Loss: 0.0005687
Epoch: 9 cost time: 1.492605209350586
Updating learning rate to 1.953125e-05
Epoch: 10, Steps: 300 | Train Loss: 0.7058251 Vali Loss: 0.0005902
EarlyStopping counter: 1 out of 5
Epoch: 10 cost time: 1.5279731750488281
Updating learning rate to 9.765625e-06
Epoch: 11, Steps: 300 | Train Loss: 0.6709348 Vali Loss: 0.0005735
EarlyStopping counter: 2 out of 5
Epoch: 11 cost time: 1.4865500926971436
Updating learning rate to 4.8828125e-06
Epoch: 12, Steps: 300 | Train Loss: 0.7055568 Vali Loss: 0.0005715
EarlyStopping counter: 3 out of 5
Epoch: 12 cost time: 1.5125501155853271
Updating learning rate to 2.44140625e-06
Epoch: 13, Steps: 300 | Train Loss: 0.7055131 Vali Loss: 0.0005691
EarlyStopping counter: 4 out of 5
Epoch: 13 cost time: 1.474722146987915
Updating learning rate to 1.220703125e-06
Epoch: 14, Steps: 300 | Train Loss: 0.7054860 Vali Loss: 0.0005721
EarlyStopping counter: 5 out of 5
Epoch: 14 cost time: 1.5109548568725586
Early stopping
Best Valid MSE: 0.0005687012654279584
>>>>>>>testing : lianghe_168_24_LightMTS_custom_ftMS_sl168_ll96_pl24_lr0.005_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_4_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
mse:0.1202647099115754, mae:0.05632906456929417
{'mae': [0.0561524454025368, 0.001448056653889112],
 'mse': [0.11743188298306087, 0.0033299537688549073]}

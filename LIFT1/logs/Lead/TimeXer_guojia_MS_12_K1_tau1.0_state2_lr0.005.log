2025-07-16 19:03:08
Args in experiment:
Namespace(task_name='long_term_forecast', is_training=1, train_only=False, wo_test=False, only_test=False, model='TimeXer', override_hyper=True, compile=False, reduce_bs=False, normalization=None, checkpoints='', root_path='./dataset/', dataset='guojia', features='MS', batch_size=8, target='OT', freq='h', wrap_data_class=[<class 'data_provider.data_loader.Dataset_Lead'>], seq_len=168, label_len=96, pred_len=12, leader_num=1, state_num=2, prefetch_path='./prefetch/guojia_L168_max', tag='_max', prefetch_batch_size=16, variable_batch_size=32, max_leader_num=16, masked_corr=False, efficient=True, pin_gpu=True, pretrain=False, freeze=False, lift=True, temperature=1.0, individual=False, fc_dropout=0.05, head_dropout=0.0, patch_len=8, stride=8, padding_patch='end', revin=1, affine=0, subtract_last=0, decomposition=0, kernel_size=25, use_norm=1, embed_type=0, d_model=256, n_heads=8, e_layers=1, d_layers=1, d_ff=1024, moving_avg=25, factor=3, distil=True, dropout=0.05, embed='timeF', activation='gelu', output_attention=False, output_enc=False, do_predict=False, seg_len=24, win_size=2, num_routers=10, subgraph_size=20, in_dim=1, gpt_layers=6, tmax=10, patch_size=16, num_workers=0, itr=1, train_epochs=100, begin_valid_epoch=0, patience=5, optim='Adam', learning_rate=0.005, des='test', loss='RMSE', lradj='type1', use_amp=False, pct_start=0.3, warmup_epochs=5, use_gpu=True, gpu=0, use_multi_gpu=False, devices='0,1,2,3', test_flop=False, local_rank=-1, enc_in=2, c_out=1, data_path='guojia.csv', dec_in=2, data='custom', model_id='guojia_168_12_TimeXer', find_unused_parameters=False)
Seed: 2023
Use GPU: cuda:0
Number of Params: 1167116 -> 1167697 (+581)
Trainable Params: 1167697 (100.0%)
Checkpoints in checkpoints/guojia_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.005_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift/checkpoint.pth
>>>>>>>start training : guojia_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.005_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift>>>>>>>>>>>>>>>>>>>>>>>>>>
train 12691
Loading prefetch files from ./prefetch/guojia_L168_max_train.npz
val 1276
Loading prefetch files from ./prefetch/guojia_L168_max_val.npz
Epoch: 1, Steps: 1586 | Train Loss: 24.6019719 Vali Loss: 0.0262834
Epoch: 1 cost time: 17.979666233062744
Updating learning rate to 0.005
Epoch: 2, Steps: 1586 | Train Loss: 0.8605541 Vali Loss: 0.0185422
Epoch: 2 cost time: 16.351783990859985
Updating learning rate to 0.0025
Epoch: 3, Steps: 1586 | Train Loss: 0.5818133 Vali Loss: 0.0111206
Epoch: 3 cost time: 16.271881818771362
Updating learning rate to 0.00125
Epoch: 4, Steps: 1586 | Train Loss: 0.4918275 Vali Loss: 0.0104745
Epoch: 4 cost time: 16.571942567825317
Updating learning rate to 0.000625
Epoch: 5, Steps: 1586 | Train Loss: 0.4053555 Vali Loss: 0.0092378
Epoch: 5 cost time: 16.409741163253784
Updating learning rate to 0.0003125
Epoch: 6, Steps: 1586 | Train Loss: 0.3883422 Vali Loss: 0.0086722
Epoch: 6 cost time: 16.339308500289917
Updating learning rate to 0.00015625
Epoch: 7, Steps: 1586 | Train Loss: 0.3743009 Vali Loss: 0.0075905
Epoch: 7 cost time: 16.37342381477356
Updating learning rate to 7.8125e-05
Epoch: 8, Steps: 1586 | Train Loss: 0.3334635 Vali Loss: 0.0070808
Epoch: 8 cost time: 16.485153675079346
Updating learning rate to 3.90625e-05
Epoch: 9, Steps: 1586 | Train Loss: 0.3097138 Vali Loss: 0.0075446
EarlyStopping counter: 1 out of 5
Epoch: 9 cost time: 16.27811360359192
Updating learning rate to 1.953125e-05
Epoch: 10, Steps: 1586 | Train Loss: 0.3050119 Vali Loss: 0.0064346
Epoch: 10 cost time: 16.295673608779907
Updating learning rate to 9.765625e-06
Epoch: 11, Steps: 1586 | Train Loss: 0.2903203 Vali Loss: 0.0062944
Epoch: 11 cost time: 16.232710361480713
Updating learning rate to 4.8828125e-06
Epoch: 12, Steps: 1586 | Train Loss: 0.2931625 Vali Loss: 0.0063744
EarlyStopping counter: 1 out of 5
Epoch: 12 cost time: 16.496009826660156
Updating learning rate to 2.44140625e-06
Epoch: 13, Steps: 1586 | Train Loss: 0.2808149 Vali Loss: 0.0063214
EarlyStopping counter: 2 out of 5
Epoch: 13 cost time: 16.312105178833008
Updating learning rate to 1.220703125e-06
Epoch: 14, Steps: 1586 | Train Loss: 0.2873919 Vali Loss: 0.0062958
EarlyStopping counter: 3 out of 5
Epoch: 14 cost time: 16.357104539871216
Updating learning rate to 6.103515625e-07
Epoch: 15, Steps: 1586 | Train Loss: 0.2796390 Vali Loss: 0.0062884
Epoch: 15 cost time: 16.42160439491272
Updating learning rate to 3.0517578125e-07
Epoch: 16, Steps: 1586 | Train Loss: 0.2829375 Vali Loss: 0.0062779
Epoch: 16 cost time: 16.349597930908203
Updating learning rate to 1.52587890625e-07
Epoch: 17, Steps: 1586 | Train Loss: 0.2800783 Vali Loss: 0.0062764
Epoch: 17 cost time: 16.32790184020996
Updating learning rate to 7.62939453125e-08
Epoch: 18, Steps: 1586 | Train Loss: 0.2859123 Vali Loss: 0.0062729
Epoch: 18 cost time: 16.33737087249756
Updating learning rate to 3.814697265625e-08
Epoch: 19, Steps: 1586 | Train Loss: 0.2841323 Vali Loss: 0.0062057
Epoch: 19 cost time: 16.416603803634644
Updating learning rate to 1.9073486328125e-08
Epoch: 20, Steps: 1586 | Train Loss: 0.2860444 Vali Loss: 0.0062726
EarlyStopping counter: 1 out of 5
Epoch: 20 cost time: 16.378305673599243
Updating learning rate to 9.5367431640625e-09
Epoch: 21, Steps: 1586 | Train Loss: 0.2855893 Vali Loss: 0.0062725
EarlyStopping counter: 2 out of 5
Epoch: 21 cost time: 16.40327763557434
Updating learning rate to 4.76837158203125e-09
Epoch: 22, Steps: 1586 | Train Loss: 0.2760096 Vali Loss: 0.0059791
Epoch: 22 cost time: 16.2950177192688
Updating learning rate to 2.384185791015625e-09
Epoch: 23, Steps: 1586 | Train Loss: 0.2822730 Vali Loss: 0.0062726
EarlyStopping counter: 1 out of 5
Epoch: 23 cost time: 16.289894342422485
Updating learning rate to 1.1920928955078125e-09
Epoch: 24, Steps: 1586 | Train Loss: 0.2777075 Vali Loss: 0.0062724
EarlyStopping counter: 2 out of 5
Epoch: 24 cost time: 16.525216102600098
Updating learning rate to 5.960464477539063e-10
Epoch: 25, Steps: 1586 | Train Loss: 0.2764989 Vali Loss: 0.0061316
EarlyStopping counter: 3 out of 5
Epoch: 25 cost time: 16.35295081138611
Updating learning rate to 2.9802322387695313e-10
Epoch: 26, Steps: 1586 | Train Loss: 0.2886246 Vali Loss: 0.0062723
EarlyStopping counter: 4 out of 5
Epoch: 26 cost time: 16.324989795684814
Updating learning rate to 1.4901161193847657e-10
Epoch: 27, Steps: 1586 | Train Loss: 0.2762560 Vali Loss: 0.0062713
EarlyStopping counter: 5 out of 5
Epoch: 27 cost time: 16.44881010055542
Early stopping
Best Valid MSE: 0.005979051316704648
>>>>>>>testing : guojia_168_12_TimeXer_custom_ftMS_sl168_ll96_pl12_lr0.005_dm256_nh8_el1_dl1_df1024_fc3_ebtimeF_dtTrue_test_0_lift<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
test 214
Loading prefetch files from ./prefetch/guojia_L168_max_test.npz
Traceback (most recent call last):
  File "/home/<USER>/Flood_flow_prediction/Flow_Prediction/LIFT/data_provider/data_loader.py", line 558, in _load_prefetch_files
    assert prefetch['leader_ids' + suffix].shape[0] == len(self.dataset) + self.pred_len
AssertionError
Fail to load prefetch files

  0%|          | 0/15 [00:00<?, ?it/s]
100%|██████████| 15/15 [00:00<00:00, 1355.74it/s]
Generate new prefetch files to ./prefetch/guojia_L168_max_test.npz
RMSE:0.12751829624176025, NSE:0.42204850912094116, R:0.7681831121444702
{'corr': [0.7681831, 0.0],
 'nse': [0.42204850912094116, 0.0],
 'rmse': [0.1275183, 0.0]}

1、修改test测试时的评估指标RMSE、R、NSE （完成）
2、测试集评估采用一定步长的滑动窗口 （完成）
    全部数据用于训练 (√)
    可以修改测试集范围 （）
3、损失函数的修改 



郭家     全部数据用于训练 (√)  修改测试集范围 （×）
城口     全部数据用于训练 (√)  修改测试集范围 （√）  0-num_test
两河     全部数据用于训练 (√)  修改测试集范围 （√）  0-num_test
沿塘     全部数据用于训练 (√)  修改测试集范围 （√）  0-num_test




# 代办 
0、调参 到更优效果 (setting.py中的prefetch_batch_size是不是也可以调参)
    - 可以尝试改变输入序列的长度可能有所提升
1、LIFT后训练后的权重没有保存 后面推理预测时需要该权重
2、（已完成）true和pred矩阵没有保存 需要保存以便于后续的绘图 
3、（已完成）考虑融入未来的降水量（外生变量） 


裁剪城口和郭家两个流域的中国产品预报数据
（已完成）考虑融入未来的降水量（外生变量） 
export CUDA_VISIBLE_DEVICES=0

model_name=VCformer
seq_len=168

# for pred_len in 12 24
for pred_len in 12
do
  python -u run.py \
    --task_name long_term_forecast \
    --is_training 1 \
    --root_path ./dataset/ \
    --data_path lianghe.csv \
    --model_id lianghe_${seq_len}_${pred_len} \
    --model $model_name \
    --data custom \
    --features MS \
    --seq_len $seq_len \
    --pred_len $pred_len \
    --loss RMSE \
    --e_layers 3 \
    --d_layers 1 \
    --factor 3 \
    --d_model 256 \
    --d_ff 1024 \
    --batch_size 8 \
    --dropout 0.1 \
    --train_epochs 20 \
    --patience 3 \
    --enc_in 2 \
    --dec_in 2 \
    --c_out 1 \
    --des 'Exp' \
    --itr 1
done
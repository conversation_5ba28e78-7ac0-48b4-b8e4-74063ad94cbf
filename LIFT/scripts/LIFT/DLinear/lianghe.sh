if [ ! -d "./logs" ]; then
    mkdir ./logs
fi

if [ ! -d "./logs/Lead" ]; then
    mkdir ./logs/Lead
fi

itr=5
seq_len=168
label_len=96
tau=1.0
data=lianghe
model_name=DLinear

for pred_len in 12 24
do
  leader_num=1
  state_num=2
  learning_rate=0.005
  python -u run_longExp.py \
    --dataset $data \
    --model $model_name \
    --seq_len $seq_len \
    --label_len $label_len \
    --pred_len $pred_len \
    --features MS \
    --itr $itr \
    --lift \
    --leader_num $leader_num --state_num $state_num --temperature $tau \
    --learning_rate $learning_rate > logs/Lead/$model_name'_'$data'_'$pred_len'_K'$leader_num'_tau'$tau'_state'$state_num'_lr'$learning_rate.log 2>&1
done
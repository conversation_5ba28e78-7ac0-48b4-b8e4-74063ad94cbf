#!/usr/bin/env python3
"""
测试RMSE损失函数的脚本
"""

import torch
import torch.nn as nn
import numpy as np

# 定义RMSE损失函数类
class RMSELoss(nn.Module):
    def __init__(self):
        super().__init__()
        self.mse = nn.MSELoss()
    
    def forward(self, pred, target):
        return torch.sqrt(self.mse(pred, target))

def test_rmse_loss():
    """测试RMSE损失函数"""
    print("测试RMSE损失函数...")
    
    # 创建测试数据
    pred = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=torch.float32)
    target = torch.tensor([[1.1, 2.1, 2.9], [3.9, 5.1, 6.1]], dtype=torch.float32)
    
    # 测试RMSE损失
    rmse_loss = RMSELoss()
    loss_value = rmse_loss(pred, target)
    
    # 手动计算RMSE验证
    mse_manual = torch.mean((pred - target) ** 2)
    rmse_manual = torch.sqrt(mse_manual)
    
    print(f"预测值: {pred}")
    print(f"真实值: {target}")
    print(f"RMSE损失值: {loss_value.item():.6f}")
    print(f"手动计算RMSE: {rmse_manual.item():.6f}")
    print(f"差异: {abs(loss_value.item() - rmse_manual.item()):.8f}")
    
    # 验证是否相等
    assert torch.allclose(loss_value, rmse_manual, atol=1e-6), "RMSE计算不正确"
    print("✅ RMSE损失函数测试通过！")
    
    # 测试梯度计算
    pred.requires_grad_(True)
    loss = rmse_loss(pred, target)
    loss.backward()
    
    print(f"梯度: {pred.grad}")
    print("✅ 梯度计算正常！")

if __name__ == "__main__":
    test_rmse_loss()

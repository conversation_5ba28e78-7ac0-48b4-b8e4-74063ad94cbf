# This source code is provided for the purposes of scientific reproducibility
# under the following limited license from Element AI Inc. The code is an
# implementation of the N-BEATS model (<PERSON><PERSON><PERSON> et al., N-BEATS: Neural basis
# expansion analysis for interpretable time series forecasting,
# https://arxiv.org/abs/1905.10437). The copyright to the source code is
# licensed under the Creative Commons - Attribution-NonCommercial 4.0
# International license (CC BY-NC 4.0):
# https://creativecommons.org/licenses/by-nc/4.0/.  Any commercial use (whether
# for the benefit of third parties or internally in production) requires an
# explicit license. The subject-matter of the N-BEATS model and associated
# materials are the property of Element AI Inc. and may be subject to patent
# protection. No license to patents is granted hereunder (whether express or
# implied). Copyright © 2020 Element AI Inc. All rights reserved.

"""
Loss functions for PyTorch.
"""

import torch as t
import torch.nn as nn
import numpy as np
import pdb


def divide_no_nan(a, b):
    """
    a/b where the resulted NaN or Inf are replaced by 0.
    """
    result = a / b
    result[result != result] = .0
    result[result == np.inf] = .0
    return result


class mape_loss(nn.Module):
    def __init__(self):
        super(mape_loss, self).__init__()

    def forward(self, insample: t.Tensor, freq: int,
                forecast: t.Tensor, target: t.Tensor, mask: t.Tensor) -> t.float:
        """
        MAPE loss as defined in: https://en.wikipedia.org/wiki/Mean_absolute_percentage_error

        :param forecast: Forecast values. Shape: batch, time
        :param target: Target values. Shape: batch, time
        :param mask: 0/1 mask. Shape: batch, time
        :return: Loss value
        """
        weights = divide_no_nan(mask, target)
        return t.mean(t.abs((forecast - target) * weights))


class smape_loss(nn.Module):
    def __init__(self):
        super(smape_loss, self).__init__()

    def forward(self, insample: t.Tensor, freq: int,
                forecast: t.Tensor, target: t.Tensor, mask: t.Tensor) -> t.float:
        """
        sMAPE loss as defined in https://robjhyndman.com/hyndsight/smape/ (Makridakis 1993)

        :param forecast: Forecast values. Shape: batch, time
        :param target: Target values. Shape: batch, time
        :param mask: 0/1 mask. Shape: batch, time
        :return: Loss value
        """
        return 200 * t.mean(divide_no_nan(t.abs(forecast - target),
                                          t.abs(forecast.data) + t.abs(target.data)) * mask)


class mase_loss(nn.Module):
    def __init__(self):
        super(mase_loss, self).__init__()

    def forward(self, insample: t.Tensor, freq: int,
                forecast: t.Tensor, target: t.Tensor, mask: t.Tensor) -> t.float:
        """
        MASE loss as defined in "Scaled Errors" https://robjhyndman.com/papers/mase.pdf

        :param insample: Insample values. Shape: batch, time_i
        :param freq: Frequency value
        :param forecast: Forecast values. Shape: batch, time_o
        :param target: Target values. Shape: batch, time_o
        :param mask: 0/1 mask. Shape: batch, time_o
        :return: Loss value
        """
        masep = t.mean(t.abs(insample[:, freq:] - insample[:, :-freq]), dim=1)
        masked_masep_inv = divide_no_nan(mask, masep[:, None])
        return t.mean(t.abs(target - forecast) * masked_masep_inv)


class RMSELoss(nn.Module):
    def __init__(self):
        super(RMSELoss, self).__init__()
        self.mse = nn.MSELoss()

    def forward(self, pred: t.Tensor, target: t.Tensor) -> t.Tensor:
        """
        RMSE loss function

        :param pred: Predicted values
        :param target: Target values
        :return: RMSE loss value
        """
        return t.sqrt(self.mse(pred, target))

class NSELoss(nn.Module):
    def __init__(self):
        super(NSELoss, self).__init__()

    def forward(self, pred: t.Tensor, target: t.Tensor) -> t.Tensor:
        """
        NSE (Nash-Sutcliffe Efficiency) loss function
        NSE = 1 - Σ(target - pred)² / Σ(target - mean(target))²
        Loss = 1 - NSE = Σ(target - pred)² / Σ(target - mean(target))²

        :param pred: Predicted values. Shape: batch, time
        :param target: Target values. Shape: batch, time
        :return: NSE loss value (1 - NSE, to minimize)
        """
        numerator = t.sum((target - pred) ** 2)
        denominator = t.sum((target - t.mean(target)) ** 2)
        return numerator / denominator


class SoftDTWLoss(nn.Module):
    def __init__(self, gamma=1.0, normalize=True):
        """
        Temporal Alignment Loss inspired by Soft-DTW

        :param gamma: Temperature parameter for soft alignment
        :param normalize: Whether to normalize by sequence length
        """
        super(SoftDTWLoss, self).__init__()
        self.gamma = gamma
        self.normalize = normalize
        self.mse = nn.MSELoss(reduction='none')

    def forward(self, pred: t.Tensor, target: t.Tensor) -> t.Tensor:
        """
        Compute temporal alignment loss

        :param pred: Predicted values. Shape: [batch, time, features] or [batch, time]
        :param target: Target values. Shape: [batch, time, features] or [batch, time]
        :return: Temporal alignment loss value
        """
        # Ensure same shape
        if pred.shape != target.shape:
            raise ValueError(f"Prediction and target shapes must match: {pred.shape} vs {target.shape}")

        # Basic MSE loss
        mse_loss = self.mse(pred, target).mean()

        # Add temporal smoothness penalty
        if pred.size(1) > 1:  # More than one time step
            # Compute differences between consecutive time steps
            pred_diff = pred[:, 1:] - pred[:, :-1]
            target_diff = target[:, 1:] - target[:, :-1]

            # Temporal alignment loss - penalize differences in temporal patterns
            temporal_loss = self.mse(pred_diff, target_diff).mean()

            # Combine MSE and temporal alignment
            total_loss = mse_loss + self.gamma * temporal_loss
        else:
            total_loss = mse_loss

        if self.normalize and pred.size(1) > 1:
            total_loss = total_loss / pred.size(1)

        return total_loss


class CombinedLoss(nn.Module):
    def __init__(self, alpha=0.5, gamma=1.0, base_loss='MSE'):
        """
        Combined loss: alpha * base_loss + (1-alpha) * soft_dtw_loss

        :param alpha: Weight for base loss (0 = only Soft-DTW, 1 = only base loss)
        :param gamma: Smoothing parameter for Soft-DTW
        :param base_loss: Base loss type ('MSE', 'RMSE', 'MAE')
        """
        super(CombinedLoss, self).__init__()
        self.alpha = alpha
        self.soft_dtw = SoftDTWLoss(gamma=gamma)

        if base_loss == 'MSE':
            self.base_loss = nn.MSELoss()
        elif base_loss == 'RMSE':
            self.base_loss = RMSELoss()
        elif base_loss == 'MAE':
            self.base_loss = nn.L1Loss()
        else:
            raise ValueError(f"Unsupported base loss: {base_loss}")

    def forward(self, pred: t.Tensor, target: t.Tensor) -> t.Tensor:
        """
        Compute combined loss

        :param pred: Predicted values
        :param target: Target values
        :return: Combined loss value
        """
        base_loss_val = self.base_loss(pred, target)
        soft_dtw_val = self.soft_dtw(pred, target)

        return self.alpha * base_loss_val + (1 - self.alpha) * soft_dtw_val


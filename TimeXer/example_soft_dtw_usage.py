#!/usr/bin/env python3
"""
Example usage of Soft-DTW loss function with TimeXer model
"""

import torch
import numpy as np
import argparse
from utils.losses import SoftDTWLoss, CombinedLoss, RMSELoss

def demonstrate_loss_functions():
    """Demonstrate different loss functions"""
    print("="*60)
    print("Soft-DTW Loss Function Demonstration")
    print("="*60)
    
    # Create sample time series data
    batch_size, seq_len, features = 4, 24, 1
    
    # Create realistic time series patterns
    t = torch.linspace(0, 4*np.pi, seq_len)
    
    # Ground truth: sine wave
    target = torch.sin(t).unsqueeze(0).unsqueeze(-1).repeat(batch_size, 1, 1)
    
    # Predictions with different types of errors
    pred1 = target.clone()  # Perfect prediction
    pred2 = torch.sin(t + 0.2).unsqueeze(0).unsqueeze(-1).repeat(batch_size, 1, 1)  # Phase shift
    pred3 = torch.sin(t).unsqueeze(0).unsqueeze(-1).repeat(batch_size, 1, 1) * 0.8  # Amplitude error
    pred4 = target + torch.randn_like(target) * 0.1  # Noisy prediction
    
    predictions = {
        'Perfect': pred1,
        'Phase Shifted': pred2, 
        'Amplitude Error': pred3,
        'Noisy': pred4
    }
    
    # Initialize loss functions
    loss_functions = {
        'RMSE': RMSELoss(),
        'Soft-DTW (γ=0.1)': SoftDTWLoss(gamma=0.1),
        'Soft-DTW (γ=1.0)': SoftDTWLoss(gamma=1.0),
        'Soft-DTW (γ=10.0)': SoftDTWLoss(gamma=10.0),
        'Combined (α=0.5)': CombinedLoss(alpha=0.5, gamma=1.0),
    }
    
    print(f"Data shapes: target={target.shape}, predictions={pred1.shape}")
    print("\nLoss Comparison:")
    print("-" * 80)
    print(f"{'Prediction Type':<15} {'RMSE':<10} {'Soft-DTW(0.1)':<12} {'Soft-DTW(1.0)':<12} {'Soft-DTW(10.0)':<13} {'Combined':<10}")
    print("-" * 80)
    
    for pred_name, pred in predictions.items():
        losses = []
        for loss_name, loss_fn in loss_functions.items():
            try:
                loss_val = loss_fn(pred, target)
                losses.append(f"{loss_val.item():.4f}")
            except Exception as e:
                losses.append("ERROR")
        
        print(f"{pred_name:<15} {losses[0]:<10} {losses[1]:<12} {losses[2]:<12} {losses[3]:<13} {losses[4]:<10}")

def create_training_example():
    """Show how to use in training"""
    print("\n" + "="*60)
    print("Training Example with Soft-DTW Loss")
    print("="*60)
    
    # Simulate model training setup
    batch_size, seq_len, features = 8, 48, 1
    
    # Create dummy model outputs and targets
    predictions = torch.randn(batch_size, seq_len, features, requires_grad=True)
    targets = torch.randn(batch_size, seq_len, features)
    
    # Initialize loss function
    criterion = SoftDTWLoss(gamma=1.0, normalize=True)
    
    print(f"Batch size: {batch_size}")
    print(f"Sequence length: {seq_len}")
    print(f"Features: {features}")
    
    # Forward pass
    loss = criterion(predictions, targets)
    print(f"Loss value: {loss.item():.6f}")
    
    # Backward pass
    loss.backward()
    
    if predictions.grad is not None:
        print(f"Gradient norm: {predictions.grad.norm().item():.6f}")
        print("✓ Gradients computed successfully")
    else:
        print("✗ No gradients computed")

def show_command_line_usage():
    """Show command line usage examples"""
    print("\n" + "="*60)
    print("Command Line Usage Examples")
    print("="*60)
    
    examples = [
        {
            'description': 'Train with Soft-DTW loss',
            'command': 'python run.py --task_name long_term_forecast --model TimeXer --data lianghe --loss SoftDTW --soft_dtw_gamma 1.0'
        },
        {
            'description': 'Train with Combined loss (50% MSE + 50% Soft-DTW)',
            'command': 'python run.py --task_name long_term_forecast --model TimeXer --data lianghe --loss Combined --combined_alpha 0.5 --soft_dtw_gamma 1.0'
        },
        {
            'description': 'Train with Combined loss (80% RMSE + 20% Soft-DTW)',
            'command': 'python run.py --task_name long_term_forecast --model TimeXer --data lianghe --loss Combined --combined_alpha 0.8 --base_loss RMSE --soft_dtw_gamma 2.0'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   {example['command']}")

def show_loss_parameters():
    """Show available loss function parameters"""
    print("\n" + "="*60)
    print("Loss Function Parameters")
    print("="*60)
    
    print("Available loss functions:")
    print("  --loss MSE          : Mean Squared Error")
    print("  --loss RMSE         : Root Mean Squared Error") 
    print("  --loss NSE          : Nash-Sutcliffe Efficiency")
    print("  --loss SoftDTW      : Soft-DTW temporal alignment loss")
    print("  --loss Combined     : Combination of base loss + Soft-DTW")
    
    print("\nSoft-DTW parameters:")
    print("  --soft_dtw_gamma    : Temperature parameter (default: 1.0)")
    print("                        Lower values = more flexible alignment")
    print("                        Higher values = stricter alignment")
    
    print("\nCombined loss parameters:")
    print("  --combined_alpha    : Weight for base loss (default: 0.5)")
    print("                        0.0 = only Soft-DTW")
    print("                        1.0 = only base loss")
    print("  --base_loss         : Base loss type (default: MSE)")
    print("                        Options: MSE, RMSE, MAE")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Soft-DTW Loss Function Demo')
    parser.add_argument('--demo', type=str, default='all', 
                       choices=['all', 'losses', 'training', 'usage', 'params'],
                       help='Which demo to run')
    
    args = parser.parse_args()
    
    if args.demo in ['all', 'losses']:
        demonstrate_loss_functions()
    
    if args.demo in ['all', 'training']:
        create_training_example()
    
    if args.demo in ['all', 'usage']:
        show_command_line_usage()
    
    if args.demo in ['all', 'params']:
        show_loss_parameters()
    
    print("\n" + "="*60)
    print("Demo completed!")
    print("="*60)

export CUDA_VISIBLE_DEVICES=1

model_name=TimeXer
des='Timexer-MS'

# 参数已调整好 prelen=12
python3 -u run.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/flow/ \
  --data_path guojia.csv \
  --model_id guojia_168_12 \
  --model $model_name \
  --data custom \
  --features MS \
  --loss Combined \
  --combined_alpha 1 \
  --base_loss RMSE \
  --soft_dtw_gamma 1 \
  --inverse \
  --batch_size 4 \
  --seq_len 168 \
  --label_len 96 \
  --pred_len 12 \
  --e_layers 1 \
  --factor 3 \
  --enc_in 2 \
  --dec_in 2 \
  --c_out 1 \
  --des $des \
  --d_model 512 \
  --d_ff 1024 \
  --patch_len 4 \
  --itr 1 


# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path guojia.csv \
#   --model_id guojia_168_24 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --loss Combined \
#   --combined_alpha 1 \
#   --base_loss RMSE \
#   --soft_dtw_gamma 1 \
#   --inverse \
#   --batch_size 4 \
#   --seq_len 168 \
#   --label_len 96 \
#   --pred_len 24 \
#   --e_layers 2 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 1 \
#   --des $des \
#   --d_model 512 \
#   --d_ff 1024 \
#   --patch_len 4 \
#   --itr 1


# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path guojia.csv \
#   --model_id guojia_168_12 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --loss RMSE \
#   --inverse \
#   --batch_size 4 \
#   --seq_len 168 \
#   --label_len 96 \
#   --pred_len 12 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 1 \
#   --des $des \
#   --d_model 512 \
#   --itr 1


# python3 -u run.py \
#   --task_name long_term_forecast \
#   --is_training 1 \
#   --root_path ./dataset/flow/ \
#   --data_path guojia.csv \
#   --model_id guojia_168_24 \
#   --model $model_name \
#   --data custom \
#   --features MS \
#   --loss Combined \
#   --combined_alpha 0 \
#   --base_loss RMSE \
#   --soft_dtw_gamma 1 \
#   --inverse \
#   --batch_size 8 \
#   --seq_len 168 \
#   --label_len 96 \
#   --pred_len 24 \
#   --e_layers 1 \
#   --factor 3 \
#   --enc_in 2 \
#   --dec_in 2 \
#   --c_out 1 \
#   --des $des \
#   --d_model 1024 \
#   --itr 1

#!/usr/bin/env python3
"""
Simple test for Soft-DTW loss function
"""

import torch
import numpy as np

def test_basic_import():
    """Test basic import"""
    try:
        from utils.losses import SoftDTWLoss, CombinedLoss, RMSELoss
        print("✓ Successfully imported loss functions")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality"""
    try:
        from utils.losses import SoftDTWLoss, RMSELoss
        
        # Create simple test data
        batch_size, seq_len, features = 1, 5, 1
        pred = torch.randn(batch_size, seq_len, features)
        target = torch.randn(batch_size, seq_len, features)
        
        print(f"Test data shapes - pred: {pred.shape}, target: {target.shape}")
        
        # Test RMSE loss
        rmse_loss = RMSELoss()
        rmse_val = rmse_loss(pred, target)
        print(f"RMSE loss: {rmse_val.item():.6f}")
        
        # Test Soft-DTW loss
        soft_dtw_loss = SoftDTWLoss(gamma=1.0)
        soft_dtw_val = soft_dtw_loss(pred, target)
        print(f"Soft-DTW loss: {soft_dtw_val.item():.6f}")
        
        print("✓ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_identical_sequences():
    """Test with identical sequences"""
    try:
        from utils.losses import SoftDTWLoss
        
        # Create identical sequences
        batch_size, seq_len, features = 1, 3, 1
        pred = torch.ones(batch_size, seq_len, features)
        target = pred.clone()
        
        soft_dtw_loss = SoftDTWLoss(gamma=1.0)
        loss_val = soft_dtw_loss(pred, target)
        
        print(f"Identical sequences loss: {loss_val.item():.6f}")
        
        if loss_val.item() < 1e-3:  # Should be very small for identical sequences
            print("✓ Identical sequences test passed")
            return True
        else:
            print("✗ Identical sequences test failed - loss too high")
            return False
            
    except Exception as e:
        print(f"✗ Identical sequences test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("="*50)
    print("Simple Soft-DTW Test")
    print("="*50)
    
    tests = [
        test_basic_import,
        test_basic_functionality,
        test_identical_sequences
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed")
